"use client"

import { useState } from "react"
import { useAuth } from "@/lib/auth-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { NewProductDialog } from "@/components/inventory/new-product-dialog"
import { StockAdjustmentDialog } from "@/components/inventory/stock-adjustment-dialog"
import { AccessDenied } from "@/components/access-denied"
import { AlertCircle, Plus, Search } from "lucide-react"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"

// Mock data - would be replaced with actual API calls
const mockProducts = [
  {
    id: "1",
    name: "Shampoo - Professional",
    sku: "SH-001",
    category: "Hair Care",
    price: 24.99,
    cost: 12.5,
    stock: 32,
    minStock: 10,
    type: "retail",
    location: "loc1",
  },
  {
    id: "2",
    name: "Conditioner - Professional",
    sku: "CO-001",
    category: "Hair Care",
    price: 22.99,
    cost: 11.25,
    stock: 28,
    minStock: 10,
    type: "retail",
    location: "loc1",
  },
  {
    id: "3",
    name: "Hair Color - Blonde",
    sku: "HC-001",
    category: "Color",
    price: 0,
    cost: 15.75,
    stock: 15,
    minStock: 5,
    type: "professional",
    location: "loc1",
  },
  {
    id: "4",
    name: "Hair Color - Brown",
    sku: "HC-002",
    category: "Color",
    price: 0,
    cost: 15.75,
    stock: 3,
    minStock: 5,
    type: "professional",
    location: "loc1",
  },
  {
    id: "5",
    name: "Styling Gel",
    sku: "SG-001",
    category: "Styling",
    price: 18.99,
    cost: 9.5,
    stock: 42,
    minStock: 15,
    type: "retail",
    location: "loc1",
  },
  {
    id: "6",
    name: "Hair Spray",
    sku: "HS-001",
    category: "Styling",
    price: 16.99,
    cost: 8.25,
    stock: 38,
    minStock: 15,
    type: "retail",
    location: "loc1",
  },
  {
    id: "7",
    name: "Treatment Mask",
    sku: "TM-001",
    category: "Treatments",
    price: 32.99,
    cost: 16.5,
    stock: 12,
    minStock: 8,
    type: "retail",
    location: "loc1",
  },
  {
    id: "8",
    name: "Bleach Powder",
    sku: "BP-001",
    category: "Color",
    price: 0,
    cost: 22.0,
    stock: 7,
    minStock: 10,
    type: "professional",
    location: "loc1",
  },
]

export default function InventoryPage() {
  const { currentLocation, hasPermission } = useAuth()
  const { formatCurrency } = useCurrency()
  const [search, setSearch] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isNewProductDialogOpen, setIsNewProductDialogOpen] = useState(false)
  const [isStockAdjustmentDialogOpen, setIsStockAdjustmentDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  // Check if user has permission to view inventory page
  if (!hasPermission("view_inventory")) {
    return (
      <AccessDenied
        description="You don't have permission to view the inventory management page."
        backButtonHref="/dashboard"
      />
    )
  }

  // Filter products based on location, search term, and active tab
  const filteredProducts = mockProducts.filter((product) => {
    // Filter by location
    if (product.location !== currentLocation && currentLocation !== "all") {
      return false
    }

    // Filter by search term
    if (
      search &&
      !product.name.toLowerCase().includes(search.toLowerCase()) &&
      !product.sku.toLowerCase().includes(search.toLowerCase())
    ) {
      return false
    }

    // Filter by tab
    if (activeTab === "retail" && product.type !== "retail") {
      return false
    }

    if (activeTab === "professional" && product.type !== "professional") {
      return false
    }

    if (activeTab === "low-stock" && product.stock >= product.minStock) {
      return false
    }

    return true
  })

  const handleAdjustStock = (product: any) => {
    setSelectedProduct(product)
    setIsStockAdjustmentDialogOpen(true)
  }

  const lowStockCount = mockProducts.filter(
    (p) => (p.location === currentLocation || currentLocation === "all") && p.stock < p.minStock,
  ).length

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
          <p className="text-muted-foreground">
            {currentLocation === "all"
              ? "Manage inventory across all locations"
              : `Manage inventory at ${currentLocation === "loc1" ? "Downtown" : currentLocation === "loc2" ? "Westside" : "Northside"} location`}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasPermission("create_inventory") && (
            <Button onClick={() => setIsNewProductDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          )}
        </div>
      </div>

      {lowStockCount > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Low Stock Alert</AlertTitle>
          <AlertDescription>
            {lowStockCount} product{lowStockCount > 1 ? "s" : ""} {lowStockCount > 1 ? "are" : "is"} below the minimum
            stock level.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader className="space-y-0 pb-2">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <CardTitle>Product Inventory</CardTitle>
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <CardDescription>Manage your salon's retail and professional products</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Products</TabsTrigger>
              <TabsTrigger value="retail">Retail</TabsTrigger>
              <TabsTrigger value="professional">Professional Use</TabsTrigger>
              <TabsTrigger value="low-stock" className="relative">
                Low Stock
                {lowStockCount > 0 && (
                  <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 h-5 min-w-5 text-xs">
                    {lowStockCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          No products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right">
                            {product.price > 0 ? <CurrencyDisplay amount={product.price} /> : "-"}
                          </TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={product.type === "retail" ? "default" : "secondary"}>
                              {product.type === "retail" ? "Retail" : "Professional"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                              Adjust
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="retail" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No retail products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.price} /></TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                              Adjust
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="professional" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          No professional products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                              Adjust
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="low-stock" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-center">Current Stock</TableHead>
                      <TableHead className="text-center">Min Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No low stock products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-center">
                            <Badge variant="destructive" className="w-16">
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">{product.minStock}</TableCell>
                          <TableCell>
                            <Badge variant={product.type === "retail" ? "default" : "secondary"}>
                              {product.type === "retail" ? "Retail" : "Professional"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                              Adjust
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <NewProductDialog open={isNewProductDialogOpen} onOpenChange={setIsNewProductDialogOpen} />

      <StockAdjustmentDialog
        open={isStockAdjustmentDialogOpen}
        onOpenChange={setIsStockAdjustmentDialogOpen}
        product={selectedProduct}
      />
    </div>
  )
}


"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { MockImage } from "@/components/mock-image"
import { Calendar } from "@/components/ui/calendar"
import { CustomCalendar } from "@/components/client-portal/custom-calendar"
import { DayPicker } from "react-day-picker"
import "react-day-picker/dist/style.css"
import { CalendarIcon } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { format, addDays, startOfDay, addHours, isBefore, parseISO, addMinutes, isWithinInterval, isSameDay } from "date-fns"
import { useToast } from "@/components/ui/use-toast"
import { ClientPortalLayout } from "@/components/client-portal/client-portal-layout"
import { mockStaff, mockAppointments, mockClients } from "@/lib/mock-data"
import { SettingsStorage } from "@/lib/settings-storage"
import { ServiceStorage } from "@/lib/service-storage"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useClients } from "@/lib/client-provider"
import { initializeAppointmentService, addAppointment, getAllAppointments, saveAppointments } from "@/lib/appointment-service"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { useLocations } from "@/lib/location-provider"
import { useServices } from "@/lib/service-provider"
import {
  Check,
  ChevronLeft,
  ChevronRight,
  Clock,
  MapPin,
  Scissors,
  User,
  Star,
  Heart,
  Palette,
  Hand,
  Sparkles,
  Brush,
  Flame,
  Flower2,
  AlertCircle,
  Info,
  BadgePercent,
  Badge as BadgeIcon
} from "lucide-react"

export default function BookAppointmentPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()
  const { clients } = useClients()

  // Client state
  const [client, setClient] = useState<any & { loyaltyPoints?: number }>(null)

  // Step state
  const [currentStep, setCurrentStep] = useState(1)

  // Form state
  const [selectedLocation, setSelectedLocation] = useState("loc1")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [selectedService, setSelectedService] = useState<string | null>(null)
  const [selectedStaff, setSelectedStaff] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedTime, setSelectedTime] = useState<string | null>(null)
  const [timeSlots, setTimeSlots] = useState<string[]>([])
  const [isBooking, setIsBooking] = useState(false)
  const [clientName, setClientName] = useState<string>("")
  const [clientPhone, setClientPhone] = useState<string>("")
  const [clientEmail, setClientEmail] = useState<string>("")
  const [isGuestCheckout, setIsGuestCheckout] = useState(false)

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date())

  // Helper function to get days in month for the calendar
  const getDaysInMonth = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Create array with empty slots for days from previous month
    const days: (number | null)[] = Array(startingDayOfWeek).fill(null);

    // Add days of current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }

    // Add empty slots to complete the grid (optional)
    const totalCells = Math.ceil(days.length / 7) * 7;
    const remainingCells = totalCells - days.length;
    for (let i = 0; i < remainingCells; i++) {
      days.push(null);
    }

    return days;
  }

  // Availability state
  const [unavailableStaff, setUnavailableStaff] = useState<string[]>([])
  const [showAvailabilityWarning, setShowAvailabilityWarning] = useState(false)
  const [loyaltyPoints, setLoyaltyPoints] = useState<number>(0)
  const [willEarnPoints, setWillEarnPoints] = useState<number>(0)
  const [bookingReference, setBookingReference] = useState<string>("")

  // Use the service provider to get services and categories
  const { services, categories, getCategoryName, refreshServices, refreshCategories } = useServices()

  // Use the locations provider
  const { locations: locationsList } = useLocations()

  // Refresh services and categories when the component mounts
  useEffect(() => {
    refreshServices()
    refreshCategories()
  }, [refreshServices, refreshCategories])

  // Get unique category names from the categories array
  const serviceCategories = categories.map(category => category.name)

  // Filter services by category and location
  const filteredServices = services.filter(service => {
    // Check if the selected category matches the service's category
    const categoryMatches = selectedCategory ?
      getCategoryName(service.category) === selectedCategory : true;

    // Check if the service is available at the selected location
    const locationMatches = !service.locations || service.locations.includes(selectedLocation);

    return categoryMatches && locationMatches;
  })

  // Get service details
  const serviceDetails = selectedService
    ? services.find(service => service.id === selectedService)
    : null

  // Get staff details
  const staffDetails = selectedStaff
    ? mockStaff.find(staff => staff.id === selectedStaff)
    : null

  // Get location data from the location provider
  const { getLocationName, isHomeServiceEnabled } = useLocations();

  // Add and remove body class for CSS targeting
  useEffect(() => {
    // Add class to body
    document.body.classList.add('booking-page');

    // Add custom styles for the date picker
    const style = document.createElement('style');
    style.id = 'custom-date-picker-styles';
    style.innerHTML = `
      .date-picker-button {
        position: relative;
        overflow: hidden;
      }

      .date-picker-button::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, rgba(236, 72, 153, 0.05), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .date-picker-button:hover::after {
        opacity: 1;
      }

      .date-picker-popover {
        animation: fadeIn 0.2s ease-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
    `;
    document.head.appendChild(style);

    // Cleanup function to remove class and styles when component unmounts
    return () => {
      document.body.classList.remove('booking-page');
      const styleElement = document.getElementById('custom-date-picker-styles');
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, []);

  // Load client data
  useEffect(() => {
    const storedClientEmail = localStorage.getItem("client_email")
    const clientId = localStorage.getItem("client_id")

    if (storedClientEmail || clientId) {
      let foundClient

      if (clientId) {
        foundClient = clients.find(c => c.id === clientId)
      } else if (storedClientEmail) {
        foundClient = clients.find(c => c.email === storedClientEmail)
      }

      if (foundClient) {
        setClient(foundClient)
        setLoyaltyPoints((foundClient as any).loyaltyPoints || 0)
        setClientName(foundClient.name || "")
        setClientPhone(foundClient.phone || "")
        setClientEmail(foundClient.email || "")
      } else {
        // Mock client for demo
        const mockClient = {
          id: "client123",
          name: "Jane Smith",
          email: storedClientEmail || "<EMAIL>",
          phone: "************",
          loyaltyPoints: 150
        }
        setClient(mockClient)
        setLoyaltyPoints(150)
        setClientName(mockClient.name)
        setClientPhone(mockClient.phone)
        setClientEmail(mockClient.email)
      }
    } else {
      // Redirect to login if no client is found
      router.push("/client-portal")
    }
  }, [clients, router])

  // Update time slots when date changes
  useEffect(() => {
    if (selectedDate) {
      // Generate time slots from 9 AM to 10 PM with 15-minute intervals
      const slots = []
      const startHour = 9
      const endHour = 22 // 10 PM
      const now = new Date()
      const isToday = isSameDay(selectedDate, now)

      // If booking for today, we need to ensure slots are at least 2 hours from now
      const currentHour = now.getHours()
      const currentMinute = now.getMinutes()

      // Calculate the minimum bookable hour (current time + 2 hours)
      const minBookableHour = isToday ? currentHour + 2 : 0

      // Round up to the nearest 15 minutes for current time
      const minuteRemainder = currentMinute % 15
      const adjustedMinute = minuteRemainder === 0 ? currentMinute : currentMinute + (15 - minuteRemainder)

      // If adjusted minute is 60, we need to move to the next hour
      const adjustForMinutes = adjustedMinute >= 60 ? 1 : 0
      const effectiveMinHour = minBookableHour + adjustForMinutes

      for (let hour = startHour; hour <= endHour; hour++) {
        // Skip hours that are less than the minimum bookable hour for today
        if (isToday && hour < effectiveMinHour) continue;

        for (let minute = 0; minute < 60; minute += 15) { // 15-minute intervals
          // For the minimum bookable hour, check if we need to skip slots
          if (isToday && hour === effectiveMinHour) {
            // Calculate the effective minute for comparison (accounting for the 2-hour buffer)
            const effectiveMinute = (adjustForMinutes === 1) ? adjustedMinute - 60 : adjustedMinute
            if (minute < effectiveMinute) continue;
          }

          // Skip times that are less than 2 hours from now
          if (isToday && hour === currentHour && minute <= currentMinute) continue;
          if (isToday && hour === currentHour + 1) {
            // Within the next hour, we need to check minutes more carefully
            if (minute <= currentMinute) continue;
          }

          const time = `${hour % 12 || 12}:${minute === 0 ? '00' : minute.toString().padStart(2, '0')} ${hour < 12 ? 'AM' : 'PM'}`
          slots.push(time)
        }
      }

      setTimeSlots(slots)

      // If no slots are available, show a message
      if (slots.length === 0 && isToday) {
        toast({
          title: "No available time slots",
          description: "There are no available time slots for today. Please select another date.",
          variant: "destructive",
        });
      }
    }
  }, [selectedDate, toast])

  // Update unavailable staff when date, time, service, or location changes
  useEffect(() => {
    if (selectedDate && selectedTime) {
      // Default duration if no service is selected yet
      let duration = 60; // Default to 60 minutes

      // If a service is selected, use its duration
      if (selectedService) {
        const service = services.find(s => s.id === selectedService);
        if (service) {
          duration = service.duration;
        }
      }

      // Check each staff member's availability
      const unavailable = mockStaff
        // Filter by role and location
        .filter(staff => {
          // For home service location, include staff with homeService flag
          if (selectedLocation === "home") {
            return staff.homeService === true;
          }
          // For regular locations, include staff assigned to that location
          return staff.locations.includes(selectedLocation);
        })
        .filter(staff => !checkStaffAvailability(staff.id, selectedDate, selectedTime, duration))
        .map(staff => staff.id);

      setUnavailableStaff(unavailable);
      setShowAvailabilityWarning(unavailable.length > 0);

      // If the currently selected staff is unavailable, clear the selection
      if (selectedStaff && unavailable.includes(selectedStaff)) {
        setSelectedStaff(null);

        toast({
          title: "Staff unavailable",
          description: "The selected stylist is not available at this time. Please choose another stylist.",
          variant: "destructive"
        });
      }
    }
  }, [selectedDate, selectedTime, selectedService, selectedStaff, selectedLocation]);

  // Update loyalty points when service changes
  useEffect(() => {
    if (selectedService) {
      const service = services.find(s => s.id === selectedService)
      if (service) {
        // Calculate loyalty points (1 point per unit of currency spent)
        const points = Math.floor(service.price)
        setWillEarnPoints(points)
      }
    } else {
      setWillEarnPoints(0)
    }
  }, [selectedService, services])

  // Check if a staff member has a day off on a specific date
  const hasStaffDayOff = (staffId: string, date: Date) => {
    try {
      // Get the day of the week (0 = Sunday, 1 = Monday, etc.)
      const dayOfWeek = date.getDay();

      // Convert to day name
      const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
      const dayName = dayNames[dayOfWeek];

      // Get staff schedules from localStorage
      const schedulesJson = localStorage.getItem('vanity_staff_schedules');
      if (!schedulesJson) return false;

      const schedules = JSON.parse(schedulesJson);

      // Find if the staff has a day off schedule for this day
      return schedules.some((schedule: any) => {
        return (
          schedule.staffId === staffId &&
          schedule.day === dayName &&
          schedule.isDayOff === true &&
          schedule.isRecurring === true
        );
      });
    } catch (error) {
      console.error("Error checking staff day off:", error);
      return false;
    }
  };

  // Check if a staff member is available at a specific time
  const checkStaffAvailability = (staffId: string, date: Date, timeString: string, duration: number) => {
    try {
      // First check if the staff has a day off on this date
      if (hasStaffDayOff(staffId, date)) {
        return false; // Staff is not available on their day off
      }

      // Parse the time string (e.g., "10:30 AM")
      const [time, period] = timeString.split(' ')
      const [hourStr, minuteStr] = time.split(':')
      let hour = parseInt(hourStr)
      const minute = parseInt(minuteStr)

      // Convert to 24-hour format
      if (period === 'PM' && hour < 12) hour += 12
      if (period === 'AM' && hour === 12) hour = 0

      // Create appointment start and end times
      const appointmentStart = new Date(date)
      appointmentStart.setHours(hour, minute, 0, 0)

      const appointmentEnd = new Date(appointmentStart)
      appointmentEnd.setMinutes(appointmentEnd.getMinutes() + duration)

      // Check if the appointment is at least 2 hours in the future
      const now = new Date()
      const twoHoursFromNow = new Date(now)
      twoHoursFromNow.setHours(now.getHours() + 2)

      if (isBefore(appointmentStart, twoHoursFromNow)) {
        return false; // Can't book less than 2 hours in advance
      }

      // Get all appointments from the appointment service to ensure sync with main app
      const allAppointments = getAllAppointments();

      // Check for conflicts with existing appointments
      const conflicts = allAppointments.some(appointment => {
        // Skip appointments that aren't for this staff member
        if (appointment.staffId !== staffId) return false

        // Skip appointments that aren't on the same day
        const appointmentDate = parseISO(appointment.date)
        if (!isSameDay(appointmentDate, date)) return false

        // Calculate the appointment's end time
        const existingAppointmentEnd = addMinutes(appointmentDate, appointment.duration)

        // Check for overlap
        return isWithinInterval(appointmentStart, { start: appointmentDate, end: existingAppointmentEnd }) ||
               isWithinInterval(appointmentEnd, { start: appointmentDate, end: existingAppointmentEnd }) ||
               (isBefore(appointmentStart, appointmentDate) && isBefore(existingAppointmentEnd, appointmentEnd))
      })

      // Staff is available if there are no conflicts
      return !conflicts
    } catch (error) {
      console.error("Error checking staff availability:", error)
      return false
    }
  }

  const handleNextStep = () => {
    // Prevent errors by wrapping in try/catch
    try {
      // Step 1: Date validation
      if (currentStep === 1 && !selectedDate) {
        toast({
          title: "Please select a date",
          description: "You need to select a date to proceed.",
          variant: "destructive",
        })
        console.log("Date selection validation failed. Current selectedDate:", selectedDate);
        return
      }

      // Step 2: Time validation
      if (currentStep === 2 && !selectedTime) {
        toast({
          title: "Please select a time",
          variant: "destructive",
        })
        return
      }

      // Step 3: Category validation
      if (currentStep === 3 && !selectedCategory) {
        toast({
          title: "Please select a service category",
          variant: "destructive",
        })
        return
      }

      // Step 4: Service validation
      if (currentStep === 4 && !selectedService) {
        toast({
          title: "Please select a service",
          variant: "destructive",
        })
        return
      }

      // Step 5: Stylist validation
      if (currentStep === 5 && !selectedStaff) {
        toast({
          title: "Please select a stylist",
          variant: "destructive",
        })
        return
      }

      // Check if selected staff is unavailable
      if (currentStep === 5 && selectedStaff && unavailableStaff.includes(selectedStaff)) {
        toast({
          title: "Staff unavailable",
          description: "The selected stylist is not available at this time. Please choose another stylist.",
          variant: "destructive",
        })
        setSelectedStaff(null)
        return
      }

      // When moving from step 5 (stylist) to step 6 (confirm), verify staff is still available
      if (currentStep === 5 && selectedTime && selectedStaff && selectedService && selectedDate) {
        const service = services.find(s => s.id === selectedService);
        if (service && !checkStaffAvailability(selectedStaff, selectedDate, selectedTime, service.duration)) {
          toast({
            title: "Staff unavailable",
            description: "The selected stylist is no longer available at this time. Please choose another stylist.",
            variant: "destructive",
          })
          return
        }
      }

      // Step 6: Contact information validation
      if (currentStep === 6) {
        if (!clientName.trim()) {
          toast({
            title: "Please enter your full name",
            variant: "destructive",
          })
          return
        }

        if (!clientPhone.trim()) {
          toast({
            title: "Please enter your phone number",
            variant: "destructive",
          })
          return
        }

        if (isGuestCheckout && !clientEmail.trim()) {
          toast({
            title: "Please enter your email address",
            variant: "destructive",
          })
          return
        }

        if (isGuestCheckout && clientEmail.trim() && !clientEmail.includes('@')) {
          toast({
            title: "Please enter a valid email address",
            variant: "destructive",
          })
          return
        }
      }

      if (currentStep < 7) {
        setCurrentStep(currentStep + 1)
      }
    } catch (error) {
      console.error("Error in handleNextStep:", error);
    }
  }

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      // If going back from service selection to category selection, clear the selected service
      if (currentStep === 4) {
        setSelectedService(null)
      }

      // If going back from category selection, clear the selected category
      if (currentStep === 3) {
        setSelectedCategory(null)
      }

      setCurrentStep(currentStep - 1)
    }
  }

  const handleGuestCheckoutToggle = (checked: boolean) => {
    setIsGuestCheckout(checked)

    if (checked) {
      // Clear client info for guest checkout
      setClientName("")
      setClientPhone("")
      setClientEmail("")
    } else {
      // Restore client info from logged in user
      setClientName(client?.name || "")
      setClientPhone(client?.phone || "")
      setClientEmail(client?.email || "")
    }
  }

  const handleBookAppointment = async () => {
    try {
      if (!selectedService || !selectedStaff || !selectedDate || !selectedTime || !client) {
        toast({
          title: "Missing information",
          description: "Please complete all required fields.",
          variant: "destructive",
        })
        return
      }

      if (!clientName.trim()) {
        toast({
          title: "Missing information",
          description: "Please enter your full name.",
          variant: "destructive",
        })
        return
      }

      if (!clientPhone.trim()) {
        toast({
          title: "Missing information",
          description: "Please enter your phone number.",
          variant: "destructive",
        })
        return
      }

      // Check if guest checkout is selected and email is provided
      if (isGuestCheckout && !clientEmail.trim()) {
        toast({
          title: "Missing information",
          description: "Please enter your email address.",
          variant: "destructive",
        })
        return
      }

      const finalClientEmail = isGuestCheckout ? clientEmail : client.email;

      setIsBooking(true)

      // Parse the time string
      const [time, period] = selectedTime.split(' ')
      const [hourStr, minuteStr] = time.split(':')
      let hour = parseInt(hourStr)
      const minute = parseInt(minuteStr)

      // Convert to 24-hour format
      if (period === 'PM' && hour < 12) hour += 12
      if (period === 'AM' && hour === 12) hour = 0

      // Create appointment date
      const appointmentDate = new Date(selectedDate)
      appointmentDate.setHours(hour, minute, 0, 0)

      // Check if the appointment is at least 2 hours in the future
      const now = new Date()
      const twoHoursFromNow = new Date(now)
      twoHoursFromNow.setHours(now.getHours() + 2)

      if (isBefore(appointmentDate, twoHoursFromNow)) {
        toast({
          title: "Booking time restriction",
          description: "Appointments must be booked at least 2 hours in advance.",
          variant: "destructive",
        })
        return
      }

      // Get service details
      const service = services.find(s => s.id === selectedService)
      if (!service) {
        throw new Error("Service not found")
      }

      // Create appointment object
      const appointment = {
        id: `appointment-${Date.now()}`,
        clientId: client.id,
        clientName: clientName || client.name,
        clientEmail: finalClientEmail,
        clientPhone: clientPhone || client.phone,
        isGuestCheckout: isGuestCheckout,
        staffId: selectedStaff,
        staffName: staffDetails?.name || "Unknown Staff",
        service: service.name,
        serviceId: service.id,
        date: appointmentDate.toISOString(),
        duration: service.duration,
        location: selectedLocation,
        price: service.price,
        notes: "",
        status: "confirmed",
        statusHistory: [
          {
            status: "pending",
            timestamp: new Date().toISOString(),
            updatedBy: "Client Portal"
          },
          {
            status: "confirmed",
            timestamp: new Date().toISOString(),
            updatedBy: "Client Portal"
          }
        ],
        type: "appointment",
        additionalServices: [],
        products: []
      }

      // Send the appointment to the API
      const response = await fetch('/api/client-portal/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(appointment),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to book appointment")
      }

      const result = await response.json()

      // Display booking reference in a more prominent toast
      toast({
        title: "Appointment Booked Successfully!",
        description: (
          <div className="space-y-2">
            <div className="font-bold text-pink-700">Booking Reference: {result.appointment.bookingReference}</div>
            <p>Your appointment has been successfully scheduled.</p>
            {willEarnPoints > 0 && <p>You'll earn {willEarnPoints} loyalty points!</p>}
            <p className="text-xs mt-1">Please save this reference number for your records.</p>
          </div>
        ),
        duration: 10000, // Show for 10 seconds to give user time to note the reference
      })

      // Add the appointment to localStorage directly as a fallback
      try {
        const storedAppointments = localStorage.getItem("vanity_appointments")
        if (storedAppointments) {
          const appointments = JSON.parse(storedAppointments)
          appointments.push(result.appointment)
          localStorage.setItem("vanity_appointments", JSON.stringify(appointments))
          console.log("Added appointment to localStorage directly")
        }
      } catch (error) {
        console.error("Error adding appointment to localStorage directly:", error)
      }

      // Initialize the appointment service to ensure all storage is in sync
      initializeAppointmentService()

      // Add the appointment to the appointment service
      addAppointment(result.appointment)

      // Get all appointments and save them to ensure consistency
      const allAppointments = getAllAppointments()
      saveAppointments(allAppointments)

      // Sync with the main app's calendar view
      try {
        // Ensure the appointment is properly formatted for the main calendar
        const formattedAppointment = {
          ...result.appointment,
          // Make sure these fields are properly set for the main calendar
          bookingReference: result.appointment.bookingReference || `REF-${Date.now().toString().slice(-6)}`,
          status: "confirmed",
          paymentStatus: "unpaid",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          source: "client_portal"
        };

        // Dispatch a custom event that the main app can listen for
        const syncEvent = new CustomEvent('appointment-created', {
          detail: { appointment: formattedAppointment }
        });
        window.dispatchEvent(syncEvent);

        // Also update any shared storage that might be used by the main app
        if (typeof window !== 'undefined' && window.localStorage) {
          // Store the last update timestamp to trigger refresh in main app
          localStorage.setItem('vanity_appointments_last_update', new Date().toISOString());

          // Store a flag indicating a new appointment was created through the client portal
          localStorage.setItem('vanity_new_client_appointment', 'true');

          // Store the appointment ID for reference
          localStorage.setItem('vanity_last_created_appointment_id', formattedAppointment.id);
        }

        console.log("Appointment synced with main app calendar:", formattedAppointment);
      } catch (error) {
        console.error("Error syncing with main app calendar:", error);
      }

      console.log("Appointment added to service and saved:", result.appointment)

      // Add loyalty points for the booking
      try {
        // Calculate loyalty points (10 points per $1 spent on services)
        const pointsToAdd = willEarnPoints;

        if (pointsToAdd > 0) {
          const loyaltyResponse = await fetch('/api/client-portal/loyalty', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              clientId: client.id,
              points: pointsToAdd,
              description: `Service: ${service.name}`
            }),
          });

          const loyaltyData = await loyaltyResponse.json();

          if (loyaltyResponse.ok) {
            console.log("Loyalty points added:", loyaltyData);

            // Update local loyalty points
            setLoyaltyPoints(prev => prev + pointsToAdd);

            // If the client reached a new tier, show a special toast
            if (loyaltyData.tierUpdated) {
              toast({
                title: "Tier Upgraded!",
                description: `Congratulations! You've reached ${loyaltyData.newTier} tier.`,
              });
            }
          }
        }
      } catch (error) {
        console.error("Error adding loyalty points:", error);
      }

      // Instead of navigating away immediately, show a success screen
      setCurrentStep(8); // Add a new step for booking success

      // Store the booking reference for display on the success screen
      setBookingReference(result.appointment.bookingReference);

      // We'll let the user navigate away manually from the success screen
    } catch (error) {
      console.error("Error booking appointment:", error)
      toast({
        title: "Error booking appointment",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsBooking(false)
    }
  }

  // Wrap the component in a try-catch to handle any rendering errors
  try {
    return (
      <ClientPortalLayout>
        <style jsx global>{`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }
          .animate-fade-in {
            animation: fadeIn 0.3s ease-out forwards;
          }

          /* Custom styling for the calendar in client portal */
          .date-picker-popover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #f9a8d4;
            width: auto !important;
            max-width: none !important;
          }

          .date-picker-button:focus {
            box-shadow: 0 0 0 2px #fdf2f8, 0 0 0 4px #db2777;
          }

          /* Fix for duplicate month headers */
          .date-picker-popover .rdp-caption {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            padding: 0 !important;
            margin-bottom: 0.5rem !important;
          }

          .date-picker-popover .rdp-caption_label {
            font-size: 1rem !important;
            font-weight: 600 !important;
            color: #be185d !important;
            margin: 0 !important;
            padding: 0 !important;
          }

          .date-picker-popover .rdp-nav {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
          }

          .date-picker-popover .rdp-multiple_months .rdp-caption:not(:first-of-type) {
            display: none !important;
          }

          /* Override calendar styles to match the new appointment dialog */
          .date-picker-popover .rdp-day_selected,
          .date-picker-popover .rdp-day_selected:focus-visible,
          .date-picker-popover .rdp-day_selected:hover {
            background-color: #db2777 !important;
            color: white !important;
          }

          .date-picker-popover .rdp-day_today {
            background-color: #fce7f3 !important;
            color: #be185d !important;
            font-weight: bold !important;
          }

          .date-picker-popover .rdp-button:hover:not([disabled]) {
            background-color: #fdf2f8 !important;
          }

          .date-picker-popover .rdp-nav_button {
            color: #db2777 !important;
          }

          .date-picker-popover .rdp-nav_button:hover {
            background-color: #fdf2f8 !important;
          }

          /* Ensure proper spacing and layout */
          .date-picker-popover .rdp {
            margin: 0 !important;
          }

          .date-picker-popover .rdp-months {
            justify-content: center !important;
          }
        `}</style>
        <div className="container mx-auto px-4 py-8" style={{ backgroundImage: 'none !important' }}>
          <div className="max-w-6xl mx-auto" style={{ backgroundImage: 'none !important' }}>
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2">Book an Appointment</h1>
              <p className="text-gray-600">
                Follow the steps below to schedule your next appointment with us.
              </p>
            </div>

            {/* Location Selector */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="h-4 w-4 text-pink-600" />
                <h3 className="font-medium">Select Location</h3>
              </div>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a location" />
                </SelectTrigger>
                <SelectContent>
                  {/* Map through active locations from settings */}
                  {locationsList
                    .filter(location => location.status === "Active" && location.enableOnlineBooking)
                    .map(location => (
                      <SelectItem key={location.id} value={location.id}>
                        {location.name}
                      </SelectItem>
                    ))
                  }

                  {/* Add Home Service option if enabled */}
                  {isHomeServiceEnabled && (
                    <SelectItem value="home">Home Service</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Progress Steps */}
            <div className="relative mb-8">
              <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 -translate-y-1/2"></div>
              <div className="relative flex justify-between">
                {[1, 2, 3, 4, 5, 6, 7].map((step) => (
                  <div key={step} className="flex flex-col items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center z-10 ${
                        step < currentStep
                          ? "bg-green-500 text-white"
                          : step === currentStep
                            ? "bg-pink-600 text-white"
                            : "bg-gray-200 text-gray-500"
                      }`}
                    >
                      {step < currentStep ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        step
                      )}
                    </div>
                    <span className={`text-xs mt-1 ${
                      step === currentStep ? "text-pink-600 font-medium" : "text-gray-500"
                    }`}>
                      {step === 1 ? "Date" :
                       step === 2 ? "Time" :
                       step === 3 ? "Category" :
                       step === 4 ? "Service" :
                       step === 5 ? "Stylist" :
                       step === 6 ? "Your Info" :
                       "Confirm"}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Step Content */}
            <Card className="mb-6 relative bg-white" style={{ backgroundImage: 'none !important' }}>
              <CardContent className="pt-6 relative z-10 bg-white" style={{ backgroundImage: 'none !important' }}>
                {/* Step 1: Select Date */}
                {currentStep === 1 && (
                  <div>
                    <h2 className="text-xl font-bold mb-4">Select a Date</h2>
                    <div className="flex flex-col items-center">
                      <div className="w-full max-w-md mx-auto mb-6">
                        <div className="bg-pink-50 p-4 rounded-lg mb-4 text-sm text-pink-800 border border-pink-200">
                          <div className="flex items-center">
                            <Info className="h-5 w-5 mr-2 flex-shrink-0 text-pink-600" />
                            <div>
                              <p className="font-medium">Booking Information</p>
                              <p>Select a date for your appointment. You can book for today (at least 2 hours in advance) or any future date.</p>
                            </div>
                          </div>
                        </div>

                        {/* Simple Calendar Component */}
                        <div className="mx-auto max-w-md bg-white rounded-lg border border-pink-200 shadow-md overflow-hidden">
                          {selectedDate && (
                            <div className="bg-pink-50 border-b border-pink-200 p-3 text-center">
                              <div className="font-medium text-pink-800 flex items-center justify-center">
                                <CalendarIcon className="h-4 w-4 mr-2 text-pink-600" />
                                <span>Selected Date: {format(selectedDate, "EEEE, MMMM d, yyyy")}</span>
                              </div>
                            </div>
                          )}

                          {/* Simple Month Navigation */}
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-4 bg-pink-50 p-2 rounded-md">
                              <button
                                type="button"
                                className="p-1 rounded-full hover:bg-white border border-pink-200"
                                onClick={() => {
                                  const newDate = new Date(currentMonth);
                                  newDate.setMonth(newDate.getMonth() - 1);
                                  setCurrentMonth(newDate);
                                }}
                              >
                                <ChevronLeft className="h-5 w-5 text-pink-600" />
                              </button>
                              <h3 className="text-lg font-semibold text-pink-700">
                                {format(currentMonth, 'MMMM yyyy')}
                              </h3>
                              <button
                                type="button"
                                className="p-1 rounded-full hover:bg-white border border-pink-200"
                                onClick={() => {
                                  const newDate = new Date(currentMonth);
                                  newDate.setMonth(newDate.getMonth() + 1);
                                  setCurrentMonth(newDate);
                                }}
                              >
                                <ChevronRight className="h-5 w-5 text-pink-600" />
                              </button>
                            </div>

                            {/* Day Headers */}
                            <div className="grid grid-cols-7 mb-2">
                              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                                <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
                                  {day}
                                </div>
                              ))}
                            </div>

                            {/* Calendar Grid */}
                            <div className="grid grid-cols-7 gap-1">
                              {getDaysInMonth().map((day, index) => {
                                const date = day ? new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day) : null;
                                const isToday = date ? isSameDay(date, new Date()) : false;
                                const isSelected = date && selectedDate ? isSameDay(date, selectedDate) : false;
                                const isPast = date ? isBefore(date, startOfDay(new Date())) : false;
                                const isSunday = date ? date.getDay() === 0 : false;
                                const isDisabled = isPast || isSunday;

                                return (
                                  <div
                                    key={index}
                                    className={`
                                      h-10 flex items-center justify-center rounded-full
                                      ${!day ? 'text-gray-300' : isDisabled ? 'text-gray-400 line-through' : 'cursor-pointer hover:bg-pink-50'}
                                      ${isSelected ? 'bg-pink-600 text-white font-semibold' : ''}
                                      ${isToday && !isSelected ? 'border-2 border-pink-500 text-pink-600 font-semibold' : ''}
                                    `}
                                    onClick={() => {
                                      if (day && !isDisabled) {
                                        const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
                                        setSelectedDate(newDate);
                                      }
                                    }}
                                  >
                                    {day || ''}
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </div>


                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Select Time */}
                {currentStep === 2 && (
                  <div>
                    <h2 className="text-xl font-bold mb-4">Select a Time</h2>
                    {timeSlots.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500 mb-4">
                          No available time slots for the selected date. Please choose another date.
                        </p>
                        <Button
                          variant="outline"
                          onClick={() => setCurrentStep(1)}
                          className="border-pink-200 text-pink-600 hover:bg-pink-50"
                        >
                          <ChevronLeft className="h-4 w-4 mr-2" />
                          Go Back to Calendar
                        </Button>
                      </div>
                    ) : (
                      <div>
                        <div className="bg-gradient-to-r from-pink-50 to-pink-100 p-4 rounded-lg mb-6 text-sm text-pink-800 flex items-start border border-pink-200 shadow-sm">
                          <Info className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5 text-pink-600" />
                          <div>
                            <p className="font-medium">Selected Date: {selectedDate ? format(selectedDate, "EEEE, MMMM d, yyyy") : ""}</p>
                            <p>Please select an available time slot for your appointment.</p>
                          </div>
                        </div>

                        {selectedTime && (
                          <div className="bg-pink-50 border border-pink-100 rounded-lg p-3 text-center w-full max-w-md mb-6">
                            <div className="font-medium text-pink-800 flex items-center justify-center">
                              <Clock className="h-4 w-4 mr-2 text-pink-600" />
                              <span>Selected Time: {selectedTime}</span>
                            </div>
                          </div>
                        )}

                        {/* Morning time slots */}
                        <div className="mb-6">
                          <h3 id="time-morning" className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <span className="bg-yellow-100 p-1 rounded-full mr-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                              </svg>
                            </span>
                            Morning (9:00 AM - 11:45 AM)
                          </h3>
                          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                            {timeSlots.filter(time => {
                              const hour = parseInt(time.split(':')[0]);
                              const period = time.split(' ')[1];
                              return (period === 'AM' && hour >= 9) || (period === 'AM' && hour === 12);
                            }).map((time) => (
                              <div key={time} className="relative">
                                <Button
                                  variant="outline"
                                  className={`w-full h-12 flex flex-col items-center justify-center p-1 ${
                                    selectedTime === time
                                      ? 'border-pink-600 bg-pink-50 text-pink-700'
                                      : 'hover:border-pink-200 hover:bg-pink-50/30'
                                  }`}
                                  onClick={() => setSelectedTime(time)}
                                >
                                  <Clock className={`h-3 w-3 mb-0.5 ${selectedTime === time ? 'text-pink-600' : 'text-gray-500'}`} />
                                  <span className="text-xs">{time}</span>
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Afternoon time slots */}
                        <div className="mb-6">
                          <h3 id="time-afternoon" className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <span className="bg-blue-100 p-1 rounded-full mr-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                              </svg>
                            </span>
                            Afternoon (12:00 PM - 4:45 PM)
                          </h3>
                          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                            {timeSlots.filter(time => {
                              const hour = parseInt(time.split(':')[0]);
                              const period = time.split(' ')[1];
                              return period === 'PM' && hour >= 12 && hour < 5;
                            }).map((time) => (
                              <div key={time} className="relative">
                                <Button
                                  variant="outline"
                                  className={`w-full h-12 flex flex-col items-center justify-center p-1 ${
                                    selectedTime === time
                                      ? 'border-pink-600 bg-pink-50 text-pink-700'
                                      : 'hover:border-pink-200 hover:bg-pink-50/30'
                                  }`}
                                  onClick={() => setSelectedTime(time)}
                                >
                                  <Clock className={`h-3 w-3 mb-0.5 ${selectedTime === time ? 'text-pink-600' : 'text-gray-500'}`} />
                                  <span className="text-xs">{time}</span>
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Evening time slots */}
                        <div className="mb-6">
                          <h3 id="time-evening" className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <span className="bg-purple-100 p-1 rounded-full mr-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                              </svg>
                            </span>
                            Evening (5:00 PM - 10:00 PM)
                          </h3>
                          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                            {timeSlots.filter(time => {
                              const hour = parseInt(time.split(':')[0]);
                              const period = time.split(' ')[1];
                              return period === 'PM' && hour >= 5;
                            }).map((time) => (
                              <div key={time} className="relative">
                                <Button
                                  variant="outline"
                                  className={`w-full h-12 flex flex-col items-center justify-center p-1 ${
                                    selectedTime === time
                                      ? 'border-pink-600 bg-pink-50 text-pink-700'
                                      : 'hover:border-pink-200 hover:bg-pink-50/30'
                                  }`}
                                  onClick={() => setSelectedTime(time)}
                                >
                                  <Clock className={`h-3 w-3 mb-0.5 ${selectedTime === time ? 'text-pink-600' : 'text-gray-500'}`} />
                                  <span className="text-xs">{time}</span>
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Time slot navigation for easier browsing */}
                        <div className="mt-4 flex justify-center">
                          <div className="flex flex-wrap gap-2 justify-center">
                            {['Morning', 'Afternoon', 'Evening'].map((timeOfDay) => (
                              <Button
                                key={timeOfDay}
                                variant="outline"
                                size="sm"
                                className="text-xs"
                                onClick={() => {
                                  const element = document.getElementById(`time-${timeOfDay.toLowerCase()}`);
                                  if (element) {
                                    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                  }
                                }}
                              >
                                {timeOfDay}
                              </Button>
                            ))}
                          </div>
                        </div>

                        {/* Hidden anchors for scrolling */}
                        <div id="time-morning" className="scroll-mt-4"></div>
                        <div id="time-afternoon" className="scroll-mt-4"></div>
                        <div id="time-evening" className="scroll-mt-4"></div>
                      </div>
                    )}
                  </div>
                )}

                {/* Step 3: Select Service Category */}
                {currentStep === 3 && (
                  <div>
                    <h2 className="text-xl font-bold mb-4">Select a Service Category</h2>

                    {categories.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500 mb-2">No service categories available at this location.</p>
                        <p className="text-sm text-gray-400">Please select a different location.</p>
                      </div>
                    ) : (
                      <div>
                        {/* Category Selection with Visual Enhancements */}
                        <div className="bg-pink-50 p-4 rounded-lg mb-6 text-sm text-pink-800 border border-pink-200">
                          <div className="flex items-center">
                            <Info className="h-5 w-5 mr-2 flex-shrink-0 text-pink-600" />
                            <div>
                              <p className="font-medium">Service Categories</p>
                              <p>Select a category to view available services at {getLocationName(selectedLocation)}.</p>
                            </div>
                          </div>
                        </div>

                        <Tabs defaultValue={selectedCategory || (categories[0]?.name || "All")} className="w-full" onValueChange={setSelectedCategory}>
                          <TabsList className="mb-6 w-full h-auto p-2 bg-muted/50 grid gap-2" style={{
                            gridTemplateColumns: `repeat(auto-fit, minmax(120px, 1fr))`,
                            gridAutoRows: 'min-content'
                          }}>
                            <TabsTrigger
                              key="all"
                              value="All"
                              className="flex items-center justify-center gap-2 h-10 px-3 py-2 text-sm font-medium whitespace-nowrap"
                            >
                              <BadgePercent className="h-4 w-4 flex-shrink-0" />
                              <span>All</span>
                            </TabsTrigger>

                            {categories.map((category) => {
                              // Get the count of services in this category for this location
                              const serviceCount = services.filter(
                                service => service.category === category.id &&
                                (!service.locations || service.locations.includes(selectedLocation))
                              ).length;

                              // Skip categories with no services at this location
                              if (serviceCount === 0) return null;

                              return (
                                <TabsTrigger
                                  key={category.id}
                                  value={category.name}
                                  className="flex items-center justify-center gap-2 h-10 px-3 py-2 text-sm font-medium whitespace-nowrap"
                                >
                                  {category.name === "Hair" && <Scissors className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Color" && <Palette className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Nails" && <Hand className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Skin" && <Sparkles className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Massage" && <Heart className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Makeup" && <Brush className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Waxing" && <Flame className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Extensions" && <Scissors className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Bridal" && <Heart className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Henna" && <Flower2 className="h-4 w-4 flex-shrink-0" />}
                                  {category.name === "Weyba Tis" && <Sparkles className="h-4 w-4 flex-shrink-0" />}
                                  <span className="truncate">{category.name}</span>
                                </TabsTrigger>
                              );
                            })}
                          </TabsList>

                          {/* All Services Tab */}
                          <TabsContent key="all" value="All" className="mt-6 space-y-4">
                            <div className="grid gap-4">
                              {filteredServices.length === 0 ? (
                                <div className="text-center py-8">
                                  <p className="text-gray-500 mb-2">No services available at this location.</p>
                                </div>
                              ) : (
                                filteredServices.map((service) => (
                                  <div
                                    key={service.id}
                                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                                      selectedService === service.id
                                        ? "border-pink-500 bg-pink-50"
                                        : "border-gray-200 hover:border-pink-300 hover:bg-pink-50/50"
                                    }`}
                                    onClick={() => {
                                      setSelectedService(service.id);
                                      setCurrentStep(4); // Move to next step automatically
                                    }}
                                  >
                                    <div className="flex justify-between items-start">
                                      <div>
                                        <h3 className="font-medium">{service.name}</h3>
                                        <p className="text-sm text-gray-500 mt-1">{service.description || `${service.name} service`}</p>
                                        <div className="flex items-center gap-3 mt-2">
                                          <div className="flex items-center text-gray-500 text-sm">
                                            <Clock className="h-3.5 w-3.5 mr-1" />
                                            {service.duration} min
                                          </div>
                                          <div className="flex items-center text-gray-500 text-sm">
                                            <Badge variant="outline" className="text-xs">
                                              {getCategoryName(service.category)}
                                            </Badge>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <p className="font-medium text-pink-600"><CurrencyDisplay amount={service.price} /></p>
                                      </div>
                                    </div>
                                  </div>
                                ))
                              )}
                            </div>
                          </TabsContent>

                          {/* Category Tabs */}
                          {categories.map((category) => {
                            // Get services for this category at this location
                            const categoryServices = services.filter(
                              service => service.category === category.id &&
                              (!service.locations || service.locations.includes(selectedLocation))
                            );

                            if (categoryServices.length === 0) return null;

                            return (
                              <TabsContent key={category.id} value={category.name} className="mt-6 space-y-4">
                                <div className="grid gap-4">
                                  {categoryServices.map((service) => (
                                    <div
                                      key={service.id}
                                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                                        selectedService === service.id
                                          ? "border-pink-500 bg-pink-50"
                                          : "border-gray-200 hover:border-pink-300 hover:bg-pink-50/50"
                                      }`}
                                      onClick={() => {
                                        setSelectedService(service.id);
                                        setCurrentStep(4); // Move to next step automatically
                                      }}
                                    >
                                      <div className="flex justify-between items-start">
                                        <div>
                                          <h3 className="font-medium">{service.name}</h3>
                                          <p className="text-sm text-gray-500 mt-1">{service.description || `${service.name} service`}</p>
                                          <div className="flex items-center gap-3 mt-2">
                                            <div className="flex items-center text-gray-500 text-sm">
                                              <Clock className="h-3.5 w-3.5 mr-1" />
                                              {service.duration} min
                                            </div>
                                          </div>
                                        </div>
                                        <div className="text-right">
                                          <p className="font-medium text-pink-600"><CurrencyDisplay amount={service.price} /></p>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </TabsContent>
                            );
                          })}
                        </Tabs>
                      </div>
                    )}
                  </div>
                )}

                {/* Step 4: Service Details */}
                {currentStep === 4 && (
                  <div>
                    <h2 className="text-xl font-bold mb-4">
                      Service Details
                    </h2>

                    {!serviceDetails ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500 mb-2">No service selected.</p>
                        <p className="text-sm text-gray-400">Please go back and select a service.</p>
                        <Button
                          variant="outline"
                          onClick={() => setCurrentStep(3)}
                          className="mt-4 border-pink-200 text-pink-600 hover:bg-pink-50"
                        >
                          <ChevronLeft className="mr-2 h-4 w-4" />
                          Go Back to Services
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        <div className="bg-pink-50 p-4 rounded-lg mb-6 text-sm text-pink-800 border border-pink-200">
                          <div className="flex items-center">
                            <Info className="h-5 w-5 mr-2 flex-shrink-0 text-pink-600" />
                            <div>
                              <p className="font-medium">Service Information</p>
                              <p>You've selected {serviceDetails.name}. Please review the details and click Next to choose your stylist.</p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white rounded-lg border border-pink-200 overflow-hidden shadow-sm">
                          <div className="bg-pink-50 border-b border-pink-200 p-3">
                            <h3 className="text-lg font-semibold text-pink-800 flex items-center">
                              <Badge className="mr-2 bg-pink-600">
                                {getCategoryName(serviceDetails.category)}
                              </Badge>
                              {serviceDetails.name}
                            </h3>
                          </div>

                          <div className="p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-gray-600">{serviceDetails.description || `${serviceDetails.name} service`}</p>

                                <div className="mt-4 flex flex-wrap gap-4">
                                  <div className="flex items-center text-gray-600">
                                    <Clock className="h-4 w-4 mr-1.5 text-pink-600" />
                                    <span>{serviceDetails.duration} minutes</span>
                                  </div>

                                  <div className="flex items-center text-gray-600">
                                    <MapPin className="h-4 w-4 mr-1.5 text-pink-600" />
                                    <span>{getLocationName(selectedLocation)}</span>
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-xl font-semibold text-pink-600"><CurrencyDisplay amount={serviceDetails.price} /></p>
                                {willEarnPoints > 0 && (
                                  <div className="text-sm text-green-600 mt-1">
                                    <Star className="h-3 w-3 inline-block mr-1" />
                                    Earn {willEarnPoints} points
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Additional service details */}
                            {serviceDetails.additionalInfo && (
                              <div className="mt-4 pt-4 border-t border-gray-100">
                                <h4 className="font-medium text-gray-700 mb-2">Additional Information</h4>
                                <p className="text-sm text-gray-600">{serviceDetails.additionalInfo}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Step 5: Select Stylist */}
                {currentStep === 5 && (
                  <div>
                    <h2 className="text-xl font-bold mb-4">Select a Stylist</h2>

                    {showAvailabilityWarning && (
                      <Alert className="mb-4 bg-amber-50 border-amber-200">
                        <AlertCircle className="h-4 w-4 text-amber-600" />
                        <AlertDescription className="text-amber-600">
                          Some stylists are unavailable at the selected time. Unavailable stylists are marked in red.
                        </AlertDescription>
                      </Alert>
                    )}

                    <RadioGroup value={selectedStaff || ""} onValueChange={setSelectedStaff}>
                      <div className="grid gap-5">
                        {mockStaff
                          .filter(staff => {
                            // For home service location, include staff with homeService flag
                            if (selectedLocation === "home") {
                              return staff.homeService === true;
                            }
                            // For regular locations, include staff assigned to that location
                            return staff.locations.includes(selectedLocation);
                          })
                          .map((staff) => {
                            const isUnavailable = unavailableStaff.includes(staff.id);
                            return (
                              <div key={staff.id} className="relative">
                                <RadioGroupItem
                                  value={staff.id}
                                  id={`staff-${staff.id}`}
                                  className="peer sr-only"
                                  disabled={isUnavailable}
                                />
                                <Label
                                  htmlFor={`staff-${staff.id}`}
                                  className={`flex items-center justify-between p-5 border rounded-lg shadow-sm ${
                                    isUnavailable
                                      ? "border-red-200 bg-red-50 opacity-60 cursor-not-allowed"
                                      : "cursor-pointer hover:border-pink-200 hover:shadow peer-data-[state=checked]:border-pink-600 peer-data-[state=checked]:bg-pink-50 transition-all"
                                  }`}
                                >
                                  <div className="flex items-center gap-5">
                                    <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 overflow-hidden">
                                      <MockImage src={`/staff-${staff.id}.jpg`} alt={staff.name} width={64} height={64} className="rounded-full" />
                                    </div>
                                    <div>
                                      <div className="flex items-center">
                                        <p className={`font-medium text-lg ${isUnavailable ? "text-red-500" : ""}`}>{staff.name}</p>
                                        {isUnavailable && (
                                          <span className="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-red-100 text-red-600 border border-red-200">
                                            Unavailable
                                          </span>
                                        )}
                                      </div>
                                      <p className="text-sm text-gray-500 capitalize mt-1">{staff.role}</p>
                                    </div>
                                  </div>
                                  <div className="flex items-center">
                                    <div className="flex text-amber-400">
                                      {[...Array(5)].map((_, i) => (
                                        <Star key={i} className="h-5 w-5 fill-amber-400" />
                                      ))}
                                    </div>
                                    <span className="text-sm text-gray-500 ml-1">(120+)</span>
                                  </div>
                                </Label>
                              </div>
                            );
                          })}
                      </div>
                    </RadioGroup>
                  </div>
                )}

                {/* Step 6: Your Information */}
                {currentStep === 6 && (
                  <div>
                    <h2 className="text-xl font-bold mb-4">
                      Your Information
                    </h2>

                    <div className="bg-white rounded-lg border p-6 mb-6">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="font-medium">Contact Details</h3>
                        <div className="flex items-center gap-2 bg-pink-50 px-3 py-1 rounded-full border border-pink-200">
                          <span className="text-xs text-pink-700">Guest Checkout</span>
                          <div className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={isGuestCheckout}
                              className="sr-only peer"
                              id="guestCheckout"
                              onChange={(e) => handleGuestCheckoutToggle(e.target.checked)}
                            />
                            <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-pink-600"></div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="clientName" className="text-sm text-gray-500">
                            Full Name
                            {!isGuestCheckout && <span className="text-xs text-gray-400 ml-1">(from account)</span>}
                          </Label>
                          <Input
                            id="clientName"
                            value={clientName}
                            onChange={(e) => setClientName(e.target.value)}
                            placeholder="Your full name"
                            className={`mt-1 ${!isGuestCheckout ? 'bg-gray-50' : ''}`}
                            readOnly={!isGuestCheckout}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="clientPhone" className="text-sm text-gray-500">
                            Phone Number
                            {!isGuestCheckout && <span className="text-xs text-gray-400 ml-1">(from account)</span>}
                          </Label>
                          <Input
                            id="clientPhone"
                            value={clientPhone}
                            onChange={(e) => setClientPhone(e.target.value)}
                            placeholder="Your phone number"
                            className={`mt-1 ${!isGuestCheckout ? 'bg-gray-50' : ''}`}
                            readOnly={!isGuestCheckout}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="clientEmail" className="text-sm text-gray-500">Email</Label>
                          {isGuestCheckout ? (
                            <Input
                              id="clientEmail"
                              type="email"
                              value={clientEmail}
                              onChange={(e) => setClientEmail(e.target.value)}
                              placeholder="Your email address"
                              className="mt-1"
                              required
                            />
                          ) : (
                            <p className="text-sm text-gray-700 mt-1 bg-gray-50 p-2 rounded border">{client?.email}</p>
                          )}
                        </div>
                      </div>

                      <div className="mt-4 text-xs text-gray-500">
                        <p>Your contact information is used to send appointment confirmations and reminders.</p>
                      </div>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg text-sm text-blue-800">
                      <p>
                        <strong>Note:</strong> Please ensure your contact information is correct as this will be used for appointment confirmations and updates.
                      </p>
                    </div>
                  </div>
                )}

                {/* Step 7: Confirmation */}
                {currentStep === 7 && (
                  <div className="relative bg-white z-10" style={{ backgroundImage: 'none !important' }}>
                    <h2 className="text-xl font-bold mb-4 confirm-your-appointment">Confirm Your Appointment</h2>
                    <div className="space-y-6 relative z-10 bg-white" style={{ backgroundImage: 'none !important' }}>
                      <div className="bg-gray-50 p-4 rounded-lg relative z-10" style={{ backgroundImage: 'none !important' }}>
                        <h3 className="font-medium mb-3">Appointment Details</h3>
                        {/* Booking reference will be shown here after booking */}
                        {bookingReference && (
                          <div className="mb-3 p-2 bg-pink-50 border border-pink-200 rounded-md">
                            <p className="text-sm font-medium text-pink-800">Booking Reference: {bookingReference}</p>
                            <p className="text-xs text-pink-600">Please save this number for your records</p>
                          </div>
                        )}
                        <div className="space-y-3">
                          <div className="flex items-start">
                            <Scissors className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <p className="font-medium">{serviceDetails?.name}</p>
                              <div className="text-sm text-gray-500">
                                <span className="bg-white px-1 rounded">{serviceDetails?.price ? <CurrencyDisplay amount={serviceDetails.price} /> : ''}</span> • {serviceDetails?.duration} min
                              </div>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <User className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <p className="font-medium">{staffDetails?.name}</p>
                              <p className="text-sm text-gray-500 capitalize">{staffDetails?.role}</p>
                              <div className="flex items-center mt-1">
                                <div className="flex text-amber-400">
                                  {[...Array(5)].map((_, i) => (
                                    <Star key={i} className="h-3.5 w-3.5 fill-amber-400" />
                                  ))}
                                </div>
                                <span className="text-xs text-gray-500 ml-1">(120+)</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <CalendarIcon className="h-5 w-5 text-pink-600 mr-3 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="font-medium">
                                {selectedDate ? format(selectedDate, "EEEE, MMMM d, yyyy") : "No date selected"}
                              </p>
                              <p className="text-sm text-gray-500">{selectedTime}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <MapPin className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <p className="font-medium">{getLocationName(selectedLocation)}</p>
                              <p className="text-sm text-gray-500">
                                {selectedLocation === "home"
                                  ? "Our stylist will come to your address"
                                  : "123 Main Street, Suite 100"}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <User className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <div className="flex items-center gap-2">
                                <p className="font-medium">Client Information</p>
                                {isGuestCheckout && (
                                  <span className="text-xs px-2 py-0.5 bg-pink-100 text-pink-700 rounded-full">Guest</span>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">{clientName}</p>
                              <p className="text-sm text-gray-500">{clientPhone}</p>
                              <p className="text-sm text-gray-500">
                                {isGuestCheckout ? clientEmail : client?.email}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="border-t pt-4 relative z-10 bg-white" style={{ backgroundImage: 'none !important' }}>
                        <div className="flex justify-between mb-2">
                          <span>Service Fee</span>
                          <span className="bg-white px-2 py-1 rounded shadow-sm">{serviceDetails?.price ? <CurrencyDisplay amount={serviceDetails.price} /> : ''}</span>
                        </div>
                        {willEarnPoints > 0 && (
                          <div className="flex justify-between mb-2 text-green-600">
                            <span>Loyalty Points</span>
                            <span>+{willEarnPoints} points</span>
                          </div>
                        )}
                        <div className="flex justify-between font-medium text-lg mt-2 pt-2 border-t">
                          <span>Total</span>
                          <span className="bg-white px-2 py-1 rounded shadow-sm">{serviceDetails?.price ? <CurrencyDisplay amount={serviceDetails.price} /> : ''}</span>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg text-sm text-blue-800">
                        <p>
                          <strong>Note:</strong> A credit card is not required to book your appointment.
                          You'll pay at the salon after your service is complete.
                        </p>
                      </div>

                      {willEarnPoints > 0 && (
                        <div className="bg-green-50 p-4 rounded-lg text-sm text-green-800 flex items-start">
                          <BadgePercent className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="font-medium">Earn {willEarnPoints} Loyalty Points</p>
                            <p>Book this appointment and earn points towards rewards!</p>
                            <div className="mt-2 flex items-center">
                              <div className="flex-1">
                                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                                  <div
                                    className="h-full bg-green-500 rounded-full"
                                    style={{ width: `${Math.min(100, (loyaltyPoints / (loyaltyPoints + 50)) * 100)}%` }}
                                  ></div>
                                </div>
                              </div>
                              <span className="ml-2 text-xs">{loyaltyPoints} / {loyaltyPoints + 50} points</span>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="bg-pink-50 border border-pink-100 p-4 rounded-lg">
                        <p className="text-sm text-pink-800">
                          By booking this appointment, you agree to our <a href="#" className="underline">cancellation policy</a>.
                          Please provide at least 24 hours notice if you need to cancel or reschedule.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 8: Booking Success */}
                {currentStep === 8 && (
                  <div id="booking-confirmed" className="booking-confirmed">
                    <div className="text-center py-6">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Check className="h-8 w-8 text-green-600" />
                      </div>
                      <h2 className="text-2xl font-bold mb-2">Booking Confirmed!</h2>
                      <p className="text-gray-600 mb-6">Your appointment has been successfully scheduled.</p>

                      <div className="bg-pink-50 border border-pink-200 rounded-lg p-4 mb-6 mx-auto max-w-md">
                        <h3 className="font-medium text-pink-800 mb-1">Booking Reference</h3>
                        <p className="text-2xl font-bold text-pink-700 mb-1">{bookingReference}</p>
                        <p className="text-sm text-pink-600">Please save this number for your records</p>
                      </div>

                      <div className="bg-gray-50 p-4 rounded-lg mb-6 mx-auto max-w-md text-left">
                        <h3 className="font-medium mb-3">Appointment Details</h3>
                        <div className="space-y-3">
                          <div className="flex items-start">
                            <Scissors className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <p className="font-medium">{serviceDetails?.name}</p>
                              <p className="text-sm text-gray-500">
                                <span className="bg-white px-1 rounded">{serviceDetails?.price ? <CurrencyDisplay amount={serviceDetails.price} /> : ''}</span> • {serviceDetails?.duration} min
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <User className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <p className="font-medium">{staffDetails?.name}</p>
                              <p className="text-sm text-gray-500 capitalize">{staffDetails?.role}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <User className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <div className="flex items-center gap-2">
                                <p className="font-medium">Client Information</p>
                                {isGuestCheckout && (
                                  <span className="text-xs px-2 py-0.5 bg-pink-100 text-pink-700 rounded-full">Guest</span>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">{clientName}</p>
                              <p className="text-sm text-gray-500">{clientPhone}</p>
                              <p className="text-sm text-gray-500">
                                {isGuestCheckout ? clientEmail : client?.email}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <CalendarIcon className="h-5 w-5 text-pink-600 mr-3 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="font-medium">
                                {selectedDate ? format(selectedDate, "EEEE, MMMM d, yyyy") : "No date selected"}
                              </p>
                              <p className="text-sm text-gray-500">{selectedTime}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <MapPin className="h-5 w-5 text-pink-600 mr-3 mt-0.5" />
                            <div>
                              <p className="font-medium">{getLocationName(selectedLocation)}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <Button
                          className="bg-pink-600 hover:bg-pink-700"
                          onClick={() => router.push("/client-portal/appointments")}
                        >
                          View My Appointments
                          <ChevronRight className="ml-2 h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => router.push("/client-portal")}
                        >
                          Return to Dashboard
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Navigation Buttons - Hide on success screen */}
            {currentStep !== 8 && (
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={currentStep === 1 ? () => router.push('/client-portal') : handlePreviousStep}
                  className="border-pink-200 text-pink-600 hover:bg-pink-50"
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  {currentStep === 1 ? "Back to Portal" : "Back"}
                </Button>

                {currentStep < 7 ? (
                  <Button
                    className="bg-pink-600 hover:bg-pink-700 text-white"
                    onClick={handleNextStep}
                    disabled={
                      (currentStep === 1 && !selectedDate) ||
                      (currentStep === 2 && !selectedTime) ||
                      (currentStep === 3 && !selectedCategory) ||
                      (currentStep === 4 && !selectedService) ||
                      (currentStep === 5 && !selectedStaff) ||
                      (currentStep === 6 && (!clientName || !clientPhone || (isGuestCheckout && !clientEmail)))
                    }
                  >
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    className="bg-pink-600 hover:bg-pink-700 text-white"
                    onClick={handleBookAppointment}
                    disabled={isBooking}
                  >
                    {isBooking ? "Booking..." : "Book Appointment"}
                    {!isBooking && <Check className="ml-2 h-4 w-4" />}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </ClientPortalLayout>
    );
  } catch (error) {
    console.error("Rendering error:", error);
    return <div>Error rendering page. Please try again later.</div>;
  }
}

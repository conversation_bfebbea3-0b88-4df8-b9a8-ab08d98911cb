// Mock loyalty data
export const loyaltyData: Record<string, {
  points: number;
  pointsToNextReward: number;
  totalPointsEarned: number;
  memberSince: string;
  tier: string;
  nextTier: string;
  pointsToNextTier: number;
  availableRewards: {
    id: string;
    name: string;
    pointsCost: number;
    expiresAt: string;
  }[];
  redeemedRewards: {
    id: string;
    rewardId: string;
    name: string;
    redeemedAt: string;
    usedAt: string | null;
    pointsCost: number;
  }[];
}> = {
  client123: {
    points: 450,
    pointsToNextReward: 50,
    totalPointsEarned: 750,
    memberSince: "Jan 15, 2025",
    tier: "Gold",
    nextTier: "Platinum",
    pointsToNextTier: 250,
    availableRewards: [
      {
        id: "r1",
        name: "$25 off your next service",
        pointsCost: 500,
        expiresAt: "2025-12-31"
      },
      {
        id: "r2",
        name: "Free product (up to $15 value)",
        pointsCost: 300,
        expiresAt: "2025-12-31"
      },
      {
        id: "r3",
        name: "Free add-on service",
        pointsCost: 200,
        expiresAt: "2025-12-31"
      }
    ],
    redeemedRewards: []
  },
  ed1: {
    points: 650,
    pointsToNextReward: 100,
    totalPointsEarned: 1250,
    memberSince: "Dec 10, 2024",
    tier: "Platinum",
    nextTier: "Diamond",
    pointsToNextTier: 500,
    availableRewards: [
      {
        id: "r1",
        name: "$25 off your next service",
        pointsCost: 500,
        expiresAt: "2025-12-31"
      },
      {
        id: "r2",
        name: "Free product (up to $15 value)",
        pointsCost: 300,
        expiresAt: "2025-12-31"
      },
      {
        id: "r3",
        name: "Free add-on service",
        pointsCost: 200,
        expiresAt: "2025-12-31"
      }
    ],
    redeemedRewards: [
      {
        id: "rr1",
        rewardId: "r1",
        name: "$25 off your next service",
        redeemedAt: "2025-01-15T10:30:00",
        usedAt: "2025-01-20T14:45:00",
        pointsCost: 500
      }
    ]
  }
};

// Mock point history
export const pointHistory: Record<string, {
  id: string;
  date: string;
  description: string;
  points: number;
  type: "earned" | "redeemed";
}[]> = {
  client123: [
    {
      id: "ph1",
      date: "2025-03-15T10:30:00",
      description: "Service: Haircut & Style",
      points: 75,
      type: "earned"
    },
    {
      id: "ph2",
      date: "2025-03-01T14:45:00",
      description: "Product Purchase: Hydrating Shampoo",
      points: 45,
      type: "earned"
    },
    {
      id: "ph3",
      date: "2025-02-15T11:15:00",
      description: "Reward Redemption: $25 off service",
      points: -250,
      type: "redeemed"
    },
    {
      id: "ph4",
      date: "2025-02-10T09:30:00",
      description: "Service: Color & Highlights",
      points: 120,
      type: "earned"
    },
    {
      id: "ph5",
      date: "2025-02-01T16:00:00",
      description: "Birthday Bonus",
      points: 250,
      type: "earned"
    },
    {
      id: "ph6",
      date: "2025-01-15T13:20:00",
      description: "Welcome Bonus",
      points: 100,
      type: "earned"
    }
  ],
  ed1: [
    {
      id: "ph1",
      date: "2025-03-20T11:30:00",
      description: "Service: Color & Highlights",
      points: 120,
      type: "earned"
    },
    {
      id: "ph2",
      date: "2025-03-05T15:45:00",
      description: "Product Purchase: Styling Kit",
      points: 75,
      type: "earned"
    },
    {
      id: "ph3",
      date: "2025-02-20T12:15:00",
      description: "Referral Bonus: James Wilson",
      points: 200,
      type: "earned"
    },
    {
      id: "ph4",
      date: "2025-01-20T14:45:00",
      description: "Reward Redemption: $25 off service",
      points: -500,
      type: "redeemed"
    },
    {
      id: "ph5",
      date: "2025-01-10T10:30:00",
      description: "Service: Haircut & Style",
      points: 75,
      type: "earned"
    },
    {
      id: "ph6",
      date: "2024-12-25T16:00:00",
      description: "Holiday Bonus",
      points: 150,
      type: "earned"
    },
    {
      id: "ph7",
      date: "2024-12-10T13:20:00",
      description: "Welcome Bonus",
      points: 100,
      type: "earned"
    }
  ]
};

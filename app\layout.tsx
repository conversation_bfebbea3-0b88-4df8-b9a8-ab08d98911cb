import type React from "react"
import { Inter } from "next/font/google"
import dynamic from "next/dynamic"
import "./globals.css"

// Dynamically import providers to avoid chunk loading issues
const ThemeProvider = dynamic(() => import("@/components/theme-provider").then(mod => mod.ThemeProvider))
const AuthProvider = dynamic(() => import("@/lib/auth-provider").then(mod => mod.AuthProvider))
const ClientProvider = dynamic(() => import("@/lib/client-provider").then(mod => mod.ClientProvider))
const CurrencyProvider = dynamic(() => import("@/lib/currency-provider").then(mod => mod.CurrencyProvider))
const LocationProvider = dynamic(() => import("@/lib/location-provider").then(mod => mod.LocationProvider))
const ServiceProvider = dynamic(() => import("@/lib/service-provider").then(mod => mod.ServiceProvider))
const ScheduleProvider = dynamic(() => import("@/lib/schedule-provider").then(mod => mod.ScheduleProvider))
const StaffProvider = dynamic(() => import("@/lib/staff-provider").then(mod => mod.StaffProvider))
const UnifiedStaffProvider = dynamic(() => import("@/lib/unified-staff-provider").then(mod => mod.UnifiedStaffProvider))
const MobileProvider = dynamic(() => import("@/hooks/use-mobile").then(mod => mod.MobileProvider))
const ClientToaster = dynamic(() => import("@/components/client-toaster").then(mod => mod.ClientToaster))
const ClientCurrencyWrapper = dynamic(() => import("@/components/client-currency-wrapper").then(mod => mod.ClientCurrencyWrapper))

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Vanity | Modern Salon Management",
  description: "Multi-location salon management application for beauty professionals",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <MobileProvider>
            <LocationProvider>
              <ServiceProvider>
                <AuthProvider>
                  <UnifiedStaffProvider>
                    <StaffProvider>
                      <ClientProvider>
                        <CurrencyProvider>
                          <ScheduleProvider>
                            <ClientCurrencyWrapper>
                              {children}
                            </ClientCurrencyWrapper>
                          </ScheduleProvider>
                        </CurrencyProvider>
                      </ClientProvider>
                    </StaffProvider>
                  </UnifiedStaffProvider>
                </AuthProvider>
              </ServiceProvider>
            </LocationProvider>
          </MobileProvider>
          <ClientToaster />
        </ThemeProvider>
      </body>
    </html>
  )
}

